"use strict";var b=Object.defineProperty;var p=(s,l)=>b(s,"name",{value:l,configurable:!0});let t=!0;const n=typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{};let r=0;if(n.process&&n.process.env&&n.process.stdout){const{FORCE_COLOR:s,NODE_DISABLE_COLORS:l,NO_COLOR:c,TERM:o,COLORTERM:i}=n.process.env;l||c||s==="0"?t=!1:s==="1"||s==="2"||s==="3"?t=!0:o==="dumb"?t=!1:"CI"in n.process.env&&["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE","DRONE"].some(g=>g in n.process.env)?t=!0:t=process.stdout.isTTY,t&&(process.platform==="win32"||i&&(i==="truecolor"||i==="24bit")?r=3:o&&(o.endsWith("-256color")||o.endsWith("256"))?r=2:r=1)}let a={enabled:t,supportLevel:r};function e(s,l,c=1){const o=`\x1B[${s}m`,i=`\x1B[${l}m`,g=new RegExp(`\\x1b\\[${l}m`,"g");return f=>a.enabled&&a.supportLevel>=c?o+(""+f).replace(g,o)+i:""+f}p(e,"kolorist");const u=e(30,39),d=e(33,39),O=e(90,39),C=e(92,39),L=e(95,39),R=e(96,39),y=e(44,49),I=e(100,49),h=e(103,49);exports.bgBlue=y,exports.bgGray=I,exports.bgLightYellow=h,exports.black=u,exports.gray=O,exports.lightCyan=R,exports.lightGreen=C,exports.lightMagenta=L,exports.options=a,exports.yellow=d;
