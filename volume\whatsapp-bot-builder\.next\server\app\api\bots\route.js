/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bots/route";
exports.ids = ["app/api/bots/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbots%2Froute&page=%2Fapi%2Fbots%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbots%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbots%2Froute&page=%2Fapi%2Fbots%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbots%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_arkit_Desktop_volume_whatsapp_bot_builder_src_app_api_bots_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/bots/route.ts */ \"(rsc)/./src/app/api/bots/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bots/route\",\n        pathname: \"/api/bots\",\n        filename: \"route\",\n        bundlePath: \"app/api/bots/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\volume\\\\whatsapp-bot-builder\\\\src\\\\app\\\\api\\\\bots\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_arkit_Desktop_volume_whatsapp_bot_builder_src_app_api_bots_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbots%2Froute&page=%2Fapi%2Fbots%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbots%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/bots/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/bots/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\nasync function POST(req) {\n    try {\n        const botData = await req.json();\n        const bot = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createBot)(botData);\n        if (!bot) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create bot'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(bot);\n    } catch (error) {\n        console.error('Error in POST /api/bots:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const userId = searchParams.get('userId');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const bots = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserBots)(userId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(bots);\n    } catch (error) {\n        console.error('Error in GET /api/bots:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9ib3RzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBdUQ7QUFDQTtBQUVoRCxlQUFlRyxLQUFLQyxHQUFnQjtJQUN6QyxJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNRCxJQUFJRSxJQUFJO1FBRTlCLE1BQU1DLE1BQU0sTUFBTU4sd0RBQVNBLENBQUNJO1FBRTVCLElBQUksQ0FBQ0UsS0FBSztZQUNSLE9BQU9QLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO2dCQUFFRSxPQUFPO1lBQXVCLEdBQ2hDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxPQUFPVCxxREFBWUEsQ0FBQ00sSUFBSSxDQUFDQztJQUMzQixFQUFFLE9BQU9DLE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBT1IscURBQVlBLENBQUNNLElBQUksQ0FDdEI7WUFBRUUsT0FBTztRQUF3QixHQUNqQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVPLGVBQWVFLElBQUlQLEdBQWdCO0lBQ3hDLElBQUk7UUFDRixNQUFNLEVBQUVRLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUlULElBQUlVLEdBQUc7UUFDeEMsTUFBTUMsU0FBU0gsYUFBYUksR0FBRyxDQUFDO1FBRWhDLElBQUksQ0FBQ0QsUUFBUTtZQUNYLE9BQU9mLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO2dCQUFFRSxPQUFPO1lBQXNCLEdBQy9CO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNUSxPQUFPLE1BQU1mLDBEQUFXQSxDQUFDYTtRQUUvQixPQUFPZixxREFBWUEsQ0FBQ00sSUFBSSxDQUFDVztJQUMzQixFQUFFLE9BQU9ULE9BQU87UUFDZEUsUUFBUUYsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsT0FBT1IscURBQVlBLENBQUNNLElBQUksQ0FDdEI7WUFBRUUsT0FBTztRQUF3QixHQUNqQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhcmtpdFxcRGVza3RvcFxcdm9sdW1lXFx3aGF0c2FwcC1ib3QtYnVpbGRlclxcc3JjXFxhcHBcXGFwaVxcYm90c1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgY3JlYXRlQm90LCBnZXRVc2VyQm90cyB9IGZyb20gJ0AvbGliL2RhdGFiYXNlJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgYm90RGF0YSA9IGF3YWl0IHJlcS5qc29uKClcbiAgICBcbiAgICBjb25zdCBib3QgPSBhd2FpdCBjcmVhdGVCb3QoYm90RGF0YSlcbiAgICBcbiAgICBpZiAoIWJvdCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGNyZWF0ZSBib3QnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKVxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oYm90KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIFBPU1QgL2FwaS9ib3RzOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxLnVybClcbiAgICBjb25zdCB1c2VySWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCd1c2VySWQnKVxuICAgIFxuICAgIGlmICghdXNlcklkKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdVc2VyIElEIGlzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG4gICAgXG4gICAgY29uc3QgYm90cyA9IGF3YWl0IGdldFVzZXJCb3RzKHVzZXJJZClcbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oYm90cylcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBHRVQgL2FwaS9ib3RzOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJjcmVhdGVCb3QiLCJnZXRVc2VyQm90cyIsIlBPU1QiLCJyZXEiLCJib3REYXRhIiwianNvbiIsImJvdCIsImVycm9yIiwic3RhdHVzIiwiY29uc29sZSIsIkdFVCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsInVzZXJJZCIsImdldCIsImJvdHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/bots/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addKnowledgeBase: () => (/* binding */ addKnowledgeBase),\n/* harmony export */   createBot: () => (/* binding */ createBot),\n/* harmony export */   createMessage: () => (/* binding */ createMessage),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   decrementUserCredits: () => (/* binding */ decrementUserCredits),\n/* harmony export */   getBotById: () => (/* binding */ getBotById),\n/* harmony export */   getBotConversations: () => (/* binding */ getBotConversations),\n/* harmony export */   getBotKnowledgeBase: () => (/* binding */ getBotKnowledgeBase),\n/* harmony export */   getConversationMessages: () => (/* binding */ getConversationMessages),\n/* harmony export */   getOrCreateConversation: () => (/* binding */ getOrCreateConversation),\n/* harmony export */   getUserBots: () => (/* binding */ getUserBots),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   trackMessageUsage: () => (/* binding */ trackMessageUsage),\n/* harmony export */   updateBot: () => (/* binding */ updateBot),\n/* harmony export */   updateUserCredits: () => (/* binding */ updateUserCredits)\n/* harmony export */ });\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(server_only__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// User operations\nasync function createUser(userData) {\n    try {\n        // Use upsert to handle existing users\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.upsert({\n            where: {\n                email: userData.email\n            },\n            update: {\n                fullName: userData.fullName || null,\n                avatarUrl: userData.avatarUrl || null\n            },\n            create: {\n                id: userData.id,\n                email: userData.email,\n                fullName: userData.fullName,\n                avatarUrl: userData.avatarUrl\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error('Error creating/updating user:', error);\n        return null;\n    }\n}\nasync function getUserById(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: userId\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error('Error fetching user:', error);\n        return null;\n    }\n}\nasync function updateUserCredits(userId, credits) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                credits\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error updating user credits:', error);\n        return false;\n    }\n}\nasync function decrementUserCredits(userId, amount = 1) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                credits: true\n            }\n        });\n        if (!user) return false;\n        const newCredits = Math.max(0, user.credits - amount);\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                credits: newCredits\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error decrementing user credits:', error);\n        return false;\n    }\n}\n// Bot operations\nasync function createBot(botData) {\n    try {\n        const bot = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.create({\n            data: botData\n        });\n        return bot;\n    } catch (error) {\n        console.error('Error creating bot:', error);\n        return null;\n    }\n}\nasync function getUserBots(userId) {\n    try {\n        const bots = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.findMany({\n            where: {\n                userId\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return bots;\n    } catch (error) {\n        console.error('Error fetching user bots:', error);\n        return [];\n    }\n}\nasync function getBotById(botId) {\n    try {\n        const bot = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.findUnique({\n            where: {\n                id: botId\n            }\n        });\n        return bot;\n    } catch (error) {\n        console.error('Error fetching bot:', error);\n        return null;\n    }\n}\nasync function updateBot(botId, updates) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.update({\n            where: {\n                id: botId\n            },\n            data: updates\n        });\n        return true;\n    } catch (error) {\n        console.error('Error updating bot:', error);\n        return false;\n    }\n}\n// Conversation operations\nasync function getOrCreateConversation(botId, customerPhone, customerName) {\n    try {\n        // Try to find existing conversation\n        const { data: existing, error: fetchError } = await supabaseAdmin.from('conversations').select('*').eq('bot_id', botId).eq('customer_phone', customerPhone).eq('status', 'active').single();\n        if (existing && !fetchError) {\n            return existing;\n        }\n        // Create new conversation\n        const { data, error } = await supabaseAdmin.from('conversations').insert({\n            bot_id: botId,\n            customer_phone: customerPhone,\n            customer_name: customerName,\n            status: 'active'\n        }).select().single();\n        if (error) throw error;\n        return data;\n    } catch (error) {\n        console.error('Error getting/creating conversation:', error);\n        return null;\n    }\n}\nasync function getBotConversations(botId) {\n    try {\n        const { data, error } = await supabase.from('conversations').select('*').eq('bot_id', botId).order('updated_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching bot conversations:', error);\n        return [];\n    }\n}\n// Message operations\nasync function createMessage(messageData) {\n    try {\n        const { data, error } = await supabaseAdmin.from('messages').insert(messageData).select().single();\n        if (error) throw error;\n        return data;\n    } catch (error) {\n        console.error('Error creating message:', error);\n        return null;\n    }\n}\nasync function getConversationMessages(conversationId) {\n    try {\n        const { data, error } = await supabase.from('messages').select('*').eq('conversation_id', conversationId).order('created_at', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching conversation messages:', error);\n        return [];\n    }\n}\n// Knowledge base operations\nasync function addKnowledgeBase(knowledgeData) {\n    try {\n        const { data, error } = await supabase.from('knowledge_base').insert(knowledgeData).select().single();\n        if (error) throw error;\n        return data;\n    } catch (error) {\n        console.error('Error adding knowledge base:', error);\n        return null;\n    }\n}\nasync function getBotKnowledgeBase(botId) {\n    try {\n        const { data, error } = await supabase.from('knowledge_base').select('*').eq('bot_id', botId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching bot knowledge base:', error);\n        return [];\n    }\n}\n// Analytics and usage\nasync function trackMessageUsage(userId, botId) {\n    try {\n        const today = new Date().toISOString().split('T')[0];\n        const { data: existing, error: fetchError } = await supabaseAdmin.from('message_usage').select('*').eq('user_id', userId).eq('bot_id', botId).eq('date', today).single();\n        if (existing && !fetchError) {\n            // Update existing record\n            const { error } = await supabaseAdmin.from('message_usage').update({\n                messages_sent: existing.messages_sent + 1\n            }).eq('id', existing.id);\n            return !error;\n        } else {\n            // Create new record\n            const { error } = await supabaseAdmin.from('message_usage').insert({\n                user_id: userId,\n                bot_id: botId,\n                messages_sent: 1,\n                date: today\n            });\n            return !error;\n        }\n    } catch (error) {\n        console.error('Error tracking message usage:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(server_only__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQjtBQUN5QjtBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXFDLEVBQUVILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhcmtpdFxcRGVza3RvcFxcdm9sdW1lXFx3aGF0c2FwcC1ib3QtYnVpbGRlclxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ3NlcnZlci1vbmx5J1xuaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbots%2Froute&page=%2Fapi%2Fbots%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbots%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();