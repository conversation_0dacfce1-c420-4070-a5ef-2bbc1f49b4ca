import 'server-only'
import { prisma } from './prisma'
import { User, Bot, Conversation, Message, KnowledgeBase, SubscriptionTier } from '@prisma/client'

// User operations
export async function createUser(userData: {
  id: string
  email: string
  fullName?: string
  avatarUrl?: string
}): Promise<User | null> {
  try {
    // Use upsert to handle existing users
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {
        fullName: userData.fullName || null,
        avatarUrl: userData.avatarUrl || null,
      },
      create: {
        id: userData.id,
        email: userData.email,
        fullName: userData.fullName,
        avatarUrl: userData.avatarUrl,
      },
    })
    return user
  } catch (error) {
    console.error('Error creating/updating user:', error)
    return null
  }
}

export async function getUserById(userId: string): Promise<User | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    })
    return user
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  }
}

export async function updateUserCredits(userId: string, credits: number): Promise<boolean> {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: { credits },
    })
    return true
  } catch (error) {
    console.error('Error updating user credits:', error)
    return false
  }
}

export async function decrementUserCredits(userId: string, amount: number = 1): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { credits: true },
    })

    if (!user) return false

    const newCredits = Math.max(0, user.credits - amount)

    await prisma.user.update({
      where: { id: userId },
      data: { credits: newCredits },
    })

    return true
  } catch (error) {
    console.error('Error decrementing user credits:', error)
    return false
  }
}

// Bot operations
export async function createBot(botData: {
  userId: string
  name: string
  description?: string
  businessContext: string
  tone?: string
  greetingMessage: string
  whatsappNumber?: string
  logoUrl?: string
}): Promise<Bot | null> {
  try {
    const bot = await prisma.bot.create({
      data: botData,
    })
    return bot
  } catch (error) {
    console.error('Error creating bot:', error)
    return null
  }
}

export async function getUserBots(userId: string): Promise<Bot[]> {
  try {
    const bots = await prisma.bot.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    })
    return bots
  } catch (error) {
    console.error('Error fetching user bots:', error)
    return []
  }
}

export async function getBotById(botId: string): Promise<Bot | null> {
  try {
    const bot = await prisma.bot.findUnique({
      where: { id: botId },
    })
    return bot
  } catch (error) {
    console.error('Error fetching bot:', error)
    return null
  }
}

export async function updateBot(botId: string, updates: Partial<Bot>): Promise<boolean> {
  try {
    await prisma.bot.update({
      where: { id: botId },
      data: updates,
    })
    return true
  } catch (error) {
    console.error('Error updating bot:', error)
    return false
  }
}

// Conversation operations
export async function getOrCreateConversation(
  botId: string,
  customerPhone: string,
  customerName?: string
): Promise<Conversation | null> {
  try {
    // Try to find existing conversation
    const { data: existing, error: fetchError } = await supabaseAdmin
      .from('conversations')
      .select('*')
      .eq('bot_id', botId)
      .eq('customer_phone', customerPhone)
      .eq('status', 'active')
      .single()

    if (existing && !fetchError) {
      return existing
    }

    // Create new conversation
    const { data, error } = await supabaseAdmin
      .from('conversations')
      .insert({
        bot_id: botId,
        customer_phone: customerPhone,
        customer_name: customerName,
        status: 'active'
      })
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error getting/creating conversation:', error)
    return null
  }
}

export async function getBotConversations(botId: string): Promise<Conversation[]> {
  try {
    const { data, error } = await supabase
      .from('conversations')
      .select('*')
      .eq('bot_id', botId)
      .order('updated_at', { ascending: false })

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error fetching bot conversations:', error)
    return []
  }
}

// Message operations
export async function createMessage(messageData: Tables['messages']['Insert']): Promise<Message | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('messages')
      .insert(messageData)
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error creating message:', error)
    return null
  }
}

export async function getConversationMessages(conversationId: string): Promise<Message[]> {
  try {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error fetching conversation messages:', error)
    return []
  }
}

// Knowledge base operations
export async function addKnowledgeBase(knowledgeData: Tables['knowledge_base']['Insert']): Promise<KnowledgeBase | null> {
  try {
    const { data, error } = await supabase
      .from('knowledge_base')
      .insert(knowledgeData)
      .select()
      .single()

    if (error) throw error
    return data
  } catch (error) {
    console.error('Error adding knowledge base:', error)
    return null
  }
}

export async function getBotKnowledgeBase(botId: string): Promise<KnowledgeBase[]> {
  try {
    const { data, error } = await supabase
      .from('knowledge_base')
      .select('*')
      .eq('bot_id', botId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  } catch (error) {
    console.error('Error fetching bot knowledge base:', error)
    return []
  }
}

// Analytics and usage
export async function trackMessageUsage(userId: string, botId: string): Promise<boolean> {
  try {
    const today = new Date().toISOString().split('T')[0]
    
    const { data: existing, error: fetchError } = await supabaseAdmin
      .from('message_usage')
      .select('*')
      .eq('user_id', userId)
      .eq('bot_id', botId)
      .eq('date', today)
      .single()

    if (existing && !fetchError) {
      // Update existing record
      const { error } = await supabaseAdmin
        .from('message_usage')
        .update({ messages_sent: existing.messages_sent + 1 })
        .eq('id', existing.id)
      
      return !error
    } else {
      // Create new record
      const { error } = await supabaseAdmin
        .from('message_usage')
        .insert({
          user_id: userId,
          bot_id: botId,
          messages_sent: 1,
          date: today
        })
      
      return !error
    }
  } catch (error) {
    console.error('Error tracking message usage:', error)
    return false
  }
}
