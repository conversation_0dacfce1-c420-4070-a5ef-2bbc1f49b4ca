"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cookie";
exports.ids = ["vendor-chunks/cookie"];
exports.modules = {

/***/ "(ssr)/./node_modules/cookie/dist/index.js":
/*!*******************************************!*\
  !*** ./node_modules/cookie/dist/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parse = parse;\nexports.serialize = serialize;\n/**\n * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n * which has been replaced by the token definition in RFC 7230 appendix B.\n *\n * cookie-name       = token\n * token             = 1*tchar\n * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n *\n * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n * Allow same range as cookie value, except `=`, which delimits end of name.\n */\nconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n/**\n * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n *\n * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n *                     ; US-ASCII characters excluding CTLs,\n *                     ; whitespace DQUOTE, comma, semicolon,\n *                     ; and backslash\n *\n * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n */\nconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n/**\n * RegExp to match domain-value in RFC 6265 sec 4.1.1\n *\n * domain-value      = <subdomain>\n *                     ; defined in [RFC1034], Section 3.5, as\n *                     ; enhanced by [RFC1123], Section 2.1\n * <subdomain>       = <label> | <subdomain> \".\" <label>\n * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n *                     Labels must be 63 characters or less.\n *                     'let-dig' not 'letter' in the first char, per RFC1123\n * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n * <let-dig-hyp>     = <let-dig> | \"-\"\n * <let-dig>         = <letter> | <digit>\n * <letter>          = any one of the 52 alphabetic characters A through Z in\n *                     upper case and a through z in lower case\n * <digit>           = any one of the ten digits 0 through 9\n *\n * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n *\n * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n * character is not permitted, but a trailing %x2E (\".\"), if present, will\n * cause the user agent to ignore the attribute.)\n */\nconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n/**\n * RegExp to match path-value in RFC 6265 sec 4.1.1\n *\n * path-value        = <any CHAR except CTLs or \";\">\n * CHAR              = %x01-7F\n *                     ; defined in RFC 5234 appendix B.1\n */\nconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\nconst __toString = Object.prototype.toString;\nconst NullObject = /* @__PURE__ */ (() => {\n    const C = function () { };\n    C.prototype = Object.create(null);\n    return C;\n})();\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n */\nfunction parse(str, options) {\n    const obj = new NullObject();\n    const len = str.length;\n    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n    if (len < 2)\n        return obj;\n    const dec = options?.decode || decode;\n    let index = 0;\n    do {\n        const eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1)\n            break; // No more cookie pairs.\n        const colonIdx = str.indexOf(\";\", index);\n        const endIdx = colonIdx === -1 ? len : colonIdx;\n        if (eqIdx > endIdx) {\n            // backtrack on prior semicolon\n            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n            continue;\n        }\n        const keyStartIdx = startIndex(str, index, eqIdx);\n        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n        const key = str.slice(keyStartIdx, keyEndIdx);\n        // only assign once\n        if (obj[key] === undefined) {\n            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n            const value = dec(str.slice(valStartIdx, valEndIdx));\n            obj[key] = value;\n        }\n        index = endIdx + 1;\n    } while (index < len);\n    return obj;\n}\nfunction startIndex(str, index, max) {\n    do {\n        const code = str.charCodeAt(index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index;\n    } while (++index < max);\n    return max;\n}\nfunction endIndex(str, index, min) {\n    while (index > min) {\n        const code = str.charCodeAt(--index);\n        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n            return index + 1;\n    }\n    return min;\n}\n/**\n * Serialize data into a cookie header.\n *\n * Serialize a name value pair into a cookie string suitable for\n * http headers. An optional options object specifies cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n */\nfunction serialize(name, val, options) {\n    const enc = options?.encode || encodeURIComponent;\n    if (!cookieNameRegExp.test(name)) {\n        throw new TypeError(`argument name is invalid: ${name}`);\n    }\n    const value = enc(val);\n    if (!cookieValueRegExp.test(value)) {\n        throw new TypeError(`argument val is invalid: ${val}`);\n    }\n    let str = name + \"=\" + value;\n    if (!options)\n        return str;\n    if (options.maxAge !== undefined) {\n        if (!Number.isInteger(options.maxAge)) {\n            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n        }\n        str += \"; Max-Age=\" + options.maxAge;\n    }\n    if (options.domain) {\n        if (!domainValueRegExp.test(options.domain)) {\n            throw new TypeError(`option domain is invalid: ${options.domain}`);\n        }\n        str += \"; Domain=\" + options.domain;\n    }\n    if (options.path) {\n        if (!pathValueRegExp.test(options.path)) {\n            throw new TypeError(`option path is invalid: ${options.path}`);\n        }\n        str += \"; Path=\" + options.path;\n    }\n    if (options.expires) {\n        if (!isDate(options.expires) ||\n            !Number.isFinite(options.expires.valueOf())) {\n            throw new TypeError(`option expires is invalid: ${options.expires}`);\n        }\n        str += \"; Expires=\" + options.expires.toUTCString();\n    }\n    if (options.httpOnly) {\n        str += \"; HttpOnly\";\n    }\n    if (options.secure) {\n        str += \"; Secure\";\n    }\n    if (options.partitioned) {\n        str += \"; Partitioned\";\n    }\n    if (options.priority) {\n        const priority = typeof options.priority === \"string\"\n            ? options.priority.toLowerCase()\n            : undefined;\n        switch (priority) {\n            case \"low\":\n                str += \"; Priority=Low\";\n                break;\n            case \"medium\":\n                str += \"; Priority=Medium\";\n                break;\n            case \"high\":\n                str += \"; Priority=High\";\n                break;\n            default:\n                throw new TypeError(`option priority is invalid: ${options.priority}`);\n        }\n    }\n    if (options.sameSite) {\n        const sameSite = typeof options.sameSite === \"string\"\n            ? options.sameSite.toLowerCase()\n            : options.sameSite;\n        switch (sameSite) {\n            case true:\n            case \"strict\":\n                str += \"; SameSite=Strict\";\n                break;\n            case \"lax\":\n                str += \"; SameSite=Lax\";\n                break;\n            case \"none\":\n                str += \"; SameSite=None\";\n                break;\n            default:\n                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n        }\n    }\n    return str;\n}\n/**\n * URL-decode string value. Optimized to skip native call when no %.\n */\nfunction decode(str) {\n    if (str.indexOf(\"%\") === -1)\n        return str;\n    try {\n        return decodeURIComponent(str);\n    }\n    catch (e) {\n        return str;\n    }\n}\n/**\n * Determine if value is a Date.\n */\nfunction isDate(val) {\n    return __toString.call(val) === \"[object Date]\";\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/cookie/dist/index.js\n");

/***/ })

};
;