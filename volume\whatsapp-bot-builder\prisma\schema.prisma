// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String   @id @default(cuid())
  email             String   @unique
  fullName          String?  @map("full_name")
  avatarUrl         String?  @map("avatar_url")
  credits           Int      @default(50)
  subscriptionTier  SubscriptionTier @default(FREE) @map("subscription_tier")
  stripeCustomerId  String?  @map("stripe_customer_id")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  bots              Bot[]
  subscriptions     UserSubscription[]
  messageUsage      MessageUsage[]

  @@map("users")
}

model Bot {
  id               String   @id @default(cuid())
  userId           String   @map("user_id")
  name             String
  description      String?
  businessContext  String   @map("business_context")
  tone             String   @default("friendly")
  greetingMessage  String   @map("greeting_message")
  whatsappNumber   String?  @map("whatsapp_number")
  logoUrl          String?  @map("logo_url")
  isActive         Boolean  @default(true) @map("is_active")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // Relations
  user             User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversations    Conversation[]
  knowledgeBase    KnowledgeBase[]
  messageUsage     MessageUsage[]

  @@map("bots")
}

model Conversation {
  id            String            @id @default(cuid())
  botId         String            @map("bot_id")
  customerPhone String            @map("customer_phone")
  customerName  String?           @map("customer_name")
  status        ConversationStatus @default(ACTIVE)
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")

  // Relations
  bot           Bot      @relation(fields: [botId], references: [id], onDelete: Cascade)
  messages      Message[]

  @@unique([botId, customerPhone])
  @@map("conversations")
}

model Message {
  id             String      @id @default(cuid())
  conversationId String      @map("conversation_id")
  content        String
  sender         MessageSender
  messageType    MessageType @default(TEXT) @map("message_type")
  mediaUrl       String?     @map("media_url")
  createdAt      DateTime    @default(now()) @map("created_at")

  // Relations
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model KnowledgeBase {
  id         String              @id @default(cuid())
  botId      String              @map("bot_id")
  content    String
  sourceType KnowledgeSourceType @map("source_type")
  sourceUrl  String?             @map("source_url")
  createdAt  DateTime            @default(now()) @map("created_at")

  // Relations
  bot        Bot                 @relation(fields: [botId], references: [id], onDelete: Cascade)

  @@map("knowledge_base")
}

model SubscriptionPlan {
  id            String           @id @default(cuid())
  name          String
  tier          SubscriptionTier
  priceMonthly  Decimal          @map("price_monthly") @db.Decimal(10, 2)
  priceYearly   Decimal?         @map("price_yearly") @db.Decimal(10, 2)
  messageLimit  Int              @map("message_limit")
  features      Json?
  stripePriceId String?          @map("stripe_price_id")
  isActive      Boolean          @default(true) @map("is_active")
  createdAt     DateTime         @default(now()) @map("created_at")

  // Relations
  subscriptions UserSubscription[]

  @@map("subscription_plans")
}

model UserSubscription {
  id                   String     @id @default(cuid())
  userId               String     @map("user_id")
  planId               String     @map("plan_id")
  stripeSubscriptionId String?    @map("stripe_subscription_id")
  status               String     @default("active")
  currentPeriodStart   DateTime?  @map("current_period_start")
  currentPeriodEnd     DateTime?  @map("current_period_end")
  createdAt            DateTime   @default(now()) @map("created_at")
  updatedAt            DateTime   @updatedAt @map("updated_at")

  // Relations
  user                 User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  plan                 SubscriptionPlan @relation(fields: [planId], references: [id])

  @@map("user_subscriptions")
}

model MessageUsage {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  botId        String   @map("bot_id")
  messagesSent Int      @default(0) @map("messages_sent")
  date         DateTime @default(now()) @db.Date
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  bot          Bot      @relation(fields: [botId], references: [id], onDelete: Cascade)

  @@unique([userId, botId, date])
  @@map("message_usage")
}

// Enums
enum SubscriptionTier {
  FREE
  STARTER
  PRO
  BUSINESS

  @@map("subscription_tier")
}

enum ConversationStatus {
  ACTIVE
  CLOSED

  @@map("conversation_status")
}

enum MessageSender {
  BOT
  CUSTOMER

  @@map("message_sender")
}

enum MessageType {
  TEXT
  IMAGE
  DOCUMENT

  @@map("message_type")
}

enum KnowledgeSourceType {
  FAQ
  PDF
  WEBSITE
  MANUAL

  @@map("knowledge_source_type")
}
