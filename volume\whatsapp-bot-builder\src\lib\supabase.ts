import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Supabase URL:', supabaseUrl)
  console.error('Supabase Anon Key:', supabaseAnonKey ? 'Present' : 'Missing')
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

// Client-side Supabase client (for authentication only)
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// Server-side Supabase client (for authentication in API routes)
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!serviceRoleKey) {
  throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable.')
}

export const supabaseAdmin = createClient(
  supabaseUrl,
  serviceRoleKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Note: We're using Supabase only for authentication.
// All database operations are handled by Prisma.

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          credits: number
          subscription_tier: 'free' | 'starter' | 'pro' | 'business'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          credits?: number
          subscription_tier?: 'free' | 'starter' | 'pro' | 'business'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          credits?: number
          subscription_tier?: 'free' | 'starter' | 'pro' | 'business'
          created_at?: string
          updated_at?: string
        }
      }
      bots: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          business_context: string
          tone: string
          greeting_message: string
          whatsapp_number: string | null
          logo_url: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          business_context: string
          tone: string
          greeting_message: string
          whatsapp_number?: string | null
          logo_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          business_context?: string
          tone?: string
          greeting_message?: string
          whatsapp_number?: string | null
          logo_url?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      conversations: {
        Row: {
          id: string
          bot_id: string
          customer_phone: string
          customer_name: string | null
          status: 'active' | 'closed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          bot_id: string
          customer_phone: string
          customer_name?: string | null
          status?: 'active' | 'closed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          bot_id?: string
          customer_phone?: string
          customer_name?: string | null
          status?: 'active' | 'closed'
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          content: string
          sender: 'bot' | 'customer'
          message_type: 'text' | 'image' | 'document'
          created_at: string
        }
        Insert: {
          id?: string
          conversation_id: string
          content: string
          sender: 'bot' | 'customer'
          message_type?: 'text' | 'image' | 'document'
          created_at?: string
        }
        Update: {
          id?: string
          conversation_id?: string
          content?: string
          sender?: 'bot' | 'customer'
          message_type?: 'text' | 'image' | 'document'
          created_at?: string
        }
      }
      knowledge_base: {
        Row: {
          id: string
          bot_id: string
          content: string
          source_type: 'faq' | 'pdf' | 'website' | 'manual'
          source_url: string | null
          created_at: string
        }
        Insert: {
          id?: string
          bot_id: string
          content: string
          source_type: 'faq' | 'pdf' | 'website' | 'manual'
          source_url?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          bot_id?: string
          content?: string
          source_type?: 'faq' | 'pdf' | 'website' | 'manual'
          source_url?: string | null
          created_at?: string
        }
      }
    }
  }
}
