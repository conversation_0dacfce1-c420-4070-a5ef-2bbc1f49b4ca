/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/route";
exports.ids = ["app/api/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_arkit_Desktop_volume_whatsapp_bot_builder_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/users/route.ts */ \"(rsc)/./src/app/api/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\volume\\\\whatsapp-bot-builder\\\\src\\\\app\\\\api\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_arkit_Desktop_volume_whatsapp_bot_builder_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/users/route.ts":
/*!************************************!*\
  !*** ./src/app/api/users/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\nasync function POST(req) {\n    try {\n        const userData = await req.json();\n        const user = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createUser)(userData);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create user'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(user);\n    } catch (error) {\n        console.error('Error in POST /api/users:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const userId = searchParams.get('id');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User ID is required'\n            }, {\n                status: 400\n            });\n        }\n        const user = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.getUserById)(userId);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User not found'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(user);\n    } catch (error) {\n        console.error('Error in GET /api/users:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS91c2Vycy9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVEO0FBQ0M7QUFFakQsZUFBZUcsS0FBS0MsR0FBZ0I7SUFDekMsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTUQsSUFBSUUsSUFBSTtRQUUvQixNQUFNQyxPQUFPLE1BQU1OLHlEQUFVQSxDQUFDSTtRQUU5QixJQUFJLENBQUNFLE1BQU07WUFDVCxPQUFPUCxxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFBRUUsT0FBTztZQUF3QixHQUNqQztnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsT0FBT1QscURBQVlBLENBQUNNLElBQUksQ0FBQ0M7SUFDM0IsRUFBRSxPQUFPQyxPQUFPO1FBQ2RFLFFBQVFGLEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU9SLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO1lBQUVFLE9BQU87UUFBd0IsR0FDakM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0Y7QUFFTyxlQUFlRSxJQUFJUCxHQUFnQjtJQUN4QyxJQUFJO1FBQ0YsTUFBTSxFQUFFUSxZQUFZLEVBQUUsR0FBRyxJQUFJQyxJQUFJVCxJQUFJVSxHQUFHO1FBQ3hDLE1BQU1DLFNBQVNILGFBQWFJLEdBQUcsQ0FBQztRQUVoQyxJQUFJLENBQUNELFFBQVE7WUFDWCxPQUFPZixxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtnQkFBRUUsT0FBTztZQUFzQixHQUMvQjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsTUFBTUYsT0FBTyxNQUFNTCwwREFBV0EsQ0FBQ2E7UUFFL0IsSUFBSSxDQUFDUixNQUFNO1lBQ1QsT0FBT1AscURBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQUVFLE9BQU87WUFBaUIsR0FDMUI7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLE9BQU9ULHFEQUFZQSxDQUFDTSxJQUFJLENBQUNDO0lBQzNCLEVBQUUsT0FBT0MsT0FBTztRQUNkRSxRQUFRRixLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPUixxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtZQUFFRSxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFya2l0XFxEZXNrdG9wXFx2b2x1bWVcXHdoYXRzYXBwLWJvdC1idWlsZGVyXFxzcmNcXGFwcFxcYXBpXFx1c2Vyc1xccm91dGUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlcXVlc3QsIE5leHRSZXNwb25zZSB9IGZyb20gJ25leHQvc2VydmVyJ1xuaW1wb3J0IHsgY3JlYXRlVXNlciwgZ2V0VXNlckJ5SWQgfSBmcm9tICdAL2xpYi9kYXRhYmFzZSdcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHVzZXJEYXRhID0gYXdhaXQgcmVxLmpzb24oKVxuICAgIFxuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBjcmVhdGVVc2VyKHVzZXJEYXRhKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnRmFpbGVkIHRvIGNyZWF0ZSB1c2VyJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgIClcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHVzZXIpXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gUE9TVCAvYXBpL3VzZXJzOicsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApXG4gIH1cbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVChyZXE6IE5leHRSZXF1ZXN0KSB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBzZWFyY2hQYXJhbXMgfSA9IG5ldyBVUkwocmVxLnVybClcbiAgICBjb25zdCB1c2VySWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpZCcpXG4gICAgXG4gICAgaWYgKCF1c2VySWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ1VzZXIgSUQgaXMgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKVxuICAgIH1cbiAgICBcbiAgICBjb25zdCB1c2VyID0gYXdhaXQgZ2V0VXNlckJ5SWQodXNlcklkKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnVXNlciBub3QgZm91bmQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDQgfVxuICAgICAgKVxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24odXNlcilcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbiBHRVQgL2FwaS91c2VyczonLCBlcnJvcilcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlVXNlciIsImdldFVzZXJCeUlkIiwiUE9TVCIsInJlcSIsInVzZXJEYXRhIiwianNvbiIsInVzZXIiLCJlcnJvciIsInN0YXR1cyIsImNvbnNvbGUiLCJHRVQiLCJzZWFyY2hQYXJhbXMiLCJVUkwiLCJ1cmwiLCJ1c2VySWQiLCJnZXQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addKnowledgeBase: () => (/* binding */ addKnowledgeBase),\n/* harmony export */   createBot: () => (/* binding */ createBot),\n/* harmony export */   createMessage: () => (/* binding */ createMessage),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   decrementUserCredits: () => (/* binding */ decrementUserCredits),\n/* harmony export */   getBotById: () => (/* binding */ getBotById),\n/* harmony export */   getBotConversations: () => (/* binding */ getBotConversations),\n/* harmony export */   getBotKnowledgeBase: () => (/* binding */ getBotKnowledgeBase),\n/* harmony export */   getConversationMessages: () => (/* binding */ getConversationMessages),\n/* harmony export */   getOrCreateConversation: () => (/* binding */ getOrCreateConversation),\n/* harmony export */   getUserBots: () => (/* binding */ getUserBots),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   trackMessageUsage: () => (/* binding */ trackMessageUsage),\n/* harmony export */   updateBot: () => (/* binding */ updateBot),\n/* harmony export */   updateUserCredits: () => (/* binding */ updateUserCredits)\n/* harmony export */ });\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(server_only__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./src/lib/prisma.ts\");\n\n\n// User operations\nasync function createUser(userData) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.create({\n            data: {\n                id: userData.id,\n                email: userData.email,\n                fullName: userData.fullName,\n                avatarUrl: userData.avatarUrl\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error('Error creating user:', error);\n        return null;\n    }\n}\nasync function getUserById(userId) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: userId\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error('Error fetching user:', error);\n        return null;\n    }\n}\nasync function updateUserCredits(userId, credits) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                credits\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error updating user credits:', error);\n        return false;\n    }\n}\nasync function decrementUserCredits(userId, amount = 1) {\n    try {\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findUnique({\n            where: {\n                id: userId\n            },\n            select: {\n                credits: true\n            }\n        });\n        if (!user) return false;\n        const newCredits = Math.max(0, user.credits - amount);\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.update({\n            where: {\n                id: userId\n            },\n            data: {\n                credits: newCredits\n            }\n        });\n        return true;\n    } catch (error) {\n        console.error('Error decrementing user credits:', error);\n        return false;\n    }\n}\n// Bot operations\nasync function createBot(botData) {\n    try {\n        const bot = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.create({\n            data: botData\n        });\n        return bot;\n    } catch (error) {\n        console.error('Error creating bot:', error);\n        return null;\n    }\n}\nasync function getUserBots(userId) {\n    try {\n        const bots = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.findMany({\n            where: {\n                userId\n            },\n            orderBy: {\n                createdAt: 'desc'\n            }\n        });\n        return bots;\n    } catch (error) {\n        console.error('Error fetching user bots:', error);\n        return [];\n    }\n}\nasync function getBotById(botId) {\n    try {\n        const bot = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.findUnique({\n            where: {\n                id: botId\n            }\n        });\n        return bot;\n    } catch (error) {\n        console.error('Error fetching bot:', error);\n        return null;\n    }\n}\nasync function updateBot(botId, updates) {\n    try {\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.bot.update({\n            where: {\n                id: botId\n            },\n            data: updates\n        });\n        return true;\n    } catch (error) {\n        console.error('Error updating bot:', error);\n        return false;\n    }\n}\n// Conversation operations\nasync function getOrCreateConversation(botId, customerPhone, customerName) {\n    try {\n        // Try to find existing conversation\n        const { data: existing, error: fetchError } = await supabaseAdmin.from('conversations').select('*').eq('bot_id', botId).eq('customer_phone', customerPhone).eq('status', 'active').single();\n        if (existing && !fetchError) {\n            return existing;\n        }\n        // Create new conversation\n        const { data, error } = await supabaseAdmin.from('conversations').insert({\n            bot_id: botId,\n            customer_phone: customerPhone,\n            customer_name: customerName,\n            status: 'active'\n        }).select().single();\n        if (error) throw error;\n        return data;\n    } catch (error) {\n        console.error('Error getting/creating conversation:', error);\n        return null;\n    }\n}\nasync function getBotConversations(botId) {\n    try {\n        const { data, error } = await supabase.from('conversations').select('*').eq('bot_id', botId).order('updated_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching bot conversations:', error);\n        return [];\n    }\n}\n// Message operations\nasync function createMessage(messageData) {\n    try {\n        const { data, error } = await supabaseAdmin.from('messages').insert(messageData).select().single();\n        if (error) throw error;\n        return data;\n    } catch (error) {\n        console.error('Error creating message:', error);\n        return null;\n    }\n}\nasync function getConversationMessages(conversationId) {\n    try {\n        const { data, error } = await supabase.from('messages').select('*').eq('conversation_id', conversationId).order('created_at', {\n            ascending: true\n        });\n        if (error) throw error;\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching conversation messages:', error);\n        return [];\n    }\n}\n// Knowledge base operations\nasync function addKnowledgeBase(knowledgeData) {\n    try {\n        const { data, error } = await supabase.from('knowledge_base').insert(knowledgeData).select().single();\n        if (error) throw error;\n        return data;\n    } catch (error) {\n        console.error('Error adding knowledge base:', error);\n        return null;\n    }\n}\nasync function getBotKnowledgeBase(botId) {\n    try {\n        const { data, error } = await supabase.from('knowledge_base').select('*').eq('bot_id', botId).order('created_at', {\n            ascending: false\n        });\n        if (error) throw error;\n        return data || [];\n    } catch (error) {\n        console.error('Error fetching bot knowledge base:', error);\n        return [];\n    }\n}\n// Analytics and usage\nasync function trackMessageUsage(userId, botId) {\n    try {\n        const today = new Date().toISOString().split('T')[0];\n        const { data: existing, error: fetchError } = await supabaseAdmin.from('message_usage').select('*').eq('user_id', userId).eq('bot_id', botId).eq('date', today).single();\n        if (existing && !fetchError) {\n            // Update existing record\n            const { error } = await supabaseAdmin.from('message_usage').update({\n                messages_sent: existing.messages_sent + 1\n            }).eq('id', existing.id);\n            return !error;\n        } else {\n            // Create new record\n            const { error } = await supabaseAdmin.from('message_usage').insert({\n                user_id: userId,\n                bot_id: botId,\n                messages_sent: 1,\n                date: today\n            });\n            return !error;\n        }\n    } catch (error) {\n        console.error('Error tracking message usage:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! server-only */ \"(rsc)/./node_modules/next/dist/compiled/server-only/empty.js\");\n/* harmony import */ var server_only__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(server_only__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQjtBQUN5QjtBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXFDLEVBQUVILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhcmtpdFxcRGVza3RvcFxcdm9sdW1lXFx3aGF0c2FwcC1ib3QtYnVpbGRlclxcc3JjXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJ3NlcnZlci1vbmx5J1xuaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYVxuIl0sIm5hbWVzIjpbIlByaXNtYUNsaWVudCIsImdsb2JhbEZvclByaXNtYSIsImdsb2JhbFRoaXMiLCJwcmlzbWEiLCJwcm9jZXNzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Carkit%5CDesktop%5Cvolume%5Cwhatsapp-bot-builder&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();