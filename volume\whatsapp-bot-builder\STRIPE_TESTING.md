# Stripe Webhook Testing Guide

This guide will help you test Stripe webhooks locally for the WhatsApp Bot Builder application.

## Prerequisites

1. **Stripe Account**: You need a Stripe account (test mode is fine)
2. **Stripe CLI**: Install the Stripe CLI for local webhook testing
3. **Environment Variables**: Set up your Stripe keys in `.env.local`

## Environment Setup

Add these variables to your `.env.local` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# App URL (for redirects)
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Method 1: Using Stripe CLI (Recommended)

### 1. Install Stripe CLI

```bash
# Windows (using Chocolatey)
choco install stripe-cli

# macOS (using Homebrew)
brew install stripe/stripe-cli/stripe

# Or download from: https://github.com/stripe/stripe-cli/releases
```

### 2. Login to Stripe

```bash
stripe login
```

### 3. Forward Webhooks to Local Server

```bash
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

This will:
- Start listening for webhook events
- Forward them to your local Next.js app
- Display the webhook signing secret (copy this to your `.env.local`)

### 4. Trigger Test Events

In another terminal, trigger test events:

```bash
# Test subscription creation
stripe trigger customer.subscription.created

# Test payment success
stripe trigger invoice.payment_succeeded

# Test subscription cancellation
stripe trigger customer.subscription.deleted

# Test payment failure
stripe trigger invoice.payment_failed
```

## Method 2: Using Our Test Script

### 1. Install Dependencies

```bash
npm install node-fetch
```

### 2. Update Webhook Secret

Edit `scripts/test-stripe-webhook.js` and update the `WEBHOOK_SECRET` with your actual webhook secret.

### 3. Run Test Script

```bash
# Test a specific event
node scripts/test-stripe-webhook.js customer.subscription.created

# Test all events
node scripts/test-stripe-webhook.js all

# Available events:
# - customer.subscription.created
# - invoice.payment_succeeded
# - customer.subscription.deleted
```

## Method 3: Using ngrok (For External Testing)

### 1. Install ngrok

```bash
# Download from https://ngrok.com/download
# Or use package managers:
npm install -g ngrok
```

### 2. Expose Local Server

```bash
ngrok http 3000
```

### 3. Configure Stripe Webhook

1. Go to your Stripe Dashboard
2. Navigate to Developers > Webhooks
3. Click "Add endpoint"
4. Use the ngrok URL: `https://your-ngrok-url.ngrok.io/api/webhooks/stripe`
5. Select events to listen for:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.created`

## Testing Scenarios

### 1. New Subscription

```bash
stripe trigger customer.subscription.created
```

**Expected behavior:**
- Creates user subscription record in database
- Updates user's subscription tier
- Resets user's message credits

### 2. Successful Payment

```bash
stripe trigger invoice.payment_succeeded
```

**Expected behavior:**
- Resets user's monthly message credits
- Logs successful payment

### 3. Failed Payment

```bash
stripe trigger invoice.payment_failed
```

**Expected behavior:**
- Logs payment failure
- Could trigger email notification (if implemented)

### 4. Subscription Cancellation

```bash
stripe trigger customer.subscription.deleted
```

**Expected behavior:**
- Updates subscription status to cancelled
- Downgrades user to free plan
- Resets credits to free plan limit (50)

## Debugging Webhooks

### 1. Check Logs

Monitor your Next.js console for webhook processing logs:

```bash
npm run dev
```

### 2. Verify Webhook Signature

The webhook endpoint verifies Stripe signatures. If you get signature verification errors:

1. Check that `STRIPE_WEBHOOK_SECRET` is correct
2. Ensure the webhook secret matches your Stripe configuration
3. Verify the endpoint URL is correct

### 3. Database Verification

After triggering webhooks, check your Supabase database:

```sql
-- Check user subscriptions
SELECT * FROM user_subscriptions ORDER BY created_at DESC;

-- Check user credits and tiers
SELECT id, email, subscription_tier, credits FROM users;

-- Check subscription plans
SELECT * FROM subscription_plans;
```

## Common Issues

### 1. Webhook Secret Mismatch

**Error:** `Webhook signature verification failed`

**Solution:** 
- Copy the webhook secret from Stripe CLI output
- Update `STRIPE_WEBHOOK_SECRET` in `.env.local`
- Restart your Next.js server

### 2. Database Connection Issues

**Error:** Database connection errors in webhook processing

**Solution:**
- Verify Supabase credentials in `.env.local`
- Check that database tables exist
- Ensure RLS policies allow service role access

### 3. Missing Stripe Customer

**Error:** `User not found for customer: cus_xxx`

**Solution:**
- Ensure users have `stripe_customer_id` set
- Test the customer creation webhook first
- Manually link test customers to users

## Production Deployment

When deploying to production:

1. **Update Webhook URL**: Change from localhost to your production domain
2. **Use Production Keys**: Switch to live Stripe keys
3. **Configure Webhook Endpoint**: Set up webhook in Stripe Dashboard
4. **Monitor Logs**: Set up proper logging and monitoring
5. **Test Thoroughly**: Run through all subscription flows

## Webhook Events Reference

| Event | Description | Action |
|-------|-------------|--------|
| `customer.subscription.created` | New subscription started | Create subscription record, upgrade user |
| `customer.subscription.updated` | Subscription modified | Update subscription details |
| `customer.subscription.deleted` | Subscription cancelled | Downgrade to free plan |
| `invoice.payment_succeeded` | Payment successful | Reset monthly credits |
| `invoice.payment_failed` | Payment failed | Log failure, notify user |
| `customer.created` | New customer created | Link customer to user account |

## Support

If you encounter issues:

1. Check the Stripe Dashboard for webhook delivery logs
2. Review Next.js console logs
3. Verify database state in Supabase
4. Test with Stripe CLI first before using custom scripts
