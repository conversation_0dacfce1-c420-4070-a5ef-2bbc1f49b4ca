import { NextRequest, NextResponse } from 'next/server'
import { createBot, getUserBots } from '@/lib/database'

export async function POST(req: NextRequest) {
  try {
    const botData = await req.json()
    
    const bot = await createBot(botData)
    
    if (!bot) {
      return NextResponse.json(
        { error: 'Failed to create bot' },
        { status: 500 }
      )
    }
    
    return NextResponse.json(bot)
  } catch (error) {
    console.error('Error in POST /api/bots:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const userId = searchParams.get('userId')
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }
    
    const bots = await getUserBots(userId)
    
    return NextResponse.json(bots)
  } catch (error) {
    console.error('Error in GET /api/bots:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
