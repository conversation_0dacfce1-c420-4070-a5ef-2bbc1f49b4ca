import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import Strip<PERSON> from 'stripe'
import { supabaseAdmin } from '@/lib/supabase'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-12-18.acacia',
})

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(req: NextRequest) {
  const body = await req.text()
  const headersList = headers()
  const sig = headersList.get('stripe-signature')

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(body, sig!, endpointSecret)
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message)
    return NextResponse.json({ error: 'Webhook signature verification failed' }, { status: 400 })
  }

  console.log('Received Stripe webhook:', event.type)

  try {
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break

      case 'customer.created':
        await handleCustomerCreated(event.data.object as Stripe.Customer)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 })
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  console.log('Subscription created:', subscription.id)
  
  const customerId = subscription.customer as string
  const priceId = subscription.items.data[0]?.price.id

  // Get user by Stripe customer ID
  const { data: user, error: userError } = await supabaseAdmin
    .from('users')
    .select('id')
    .eq('stripe_customer_id', customerId)
    .single()

  if (userError || !user) {
    console.error('User not found for customer:', customerId)
    return
  }

  // Get subscription plan by Stripe price ID
  const { data: plan, error: planError } = await supabaseAdmin
    .from('subscription_plans')
    .select('*')
    .eq('stripe_price_id', priceId)
    .single()

  if (planError || !plan) {
    console.error('Plan not found for price:', priceId)
    return
  }

  // Create user subscription record
  const { error: subscriptionError } = await supabaseAdmin
    .from('user_subscriptions')
    .insert({
      user_id: user.id,
      plan_id: plan.id,
      stripe_subscription_id: subscription.id,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    })

  if (subscriptionError) {
    console.error('Error creating subscription record:', subscriptionError)
    return
  }

  // Update user's subscription tier and credits
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      subscription_tier: plan.tier,
      credits: plan.message_limit,
    })
    .eq('id', user.id)

  if (updateError) {
    console.error('Error updating user:', updateError)
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  console.log('Subscription updated:', subscription.id)
  
  const { error } = await supabaseAdmin
    .from('user_subscriptions')
    .update({
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription:', error)
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  console.log('Subscription deleted:', subscription.id)
  
  // Get the subscription record
  const { data: userSub, error: subError } = await supabaseAdmin
    .from('user_subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single()

  if (subError || !userSub) {
    console.error('Subscription record not found:', subscription.id)
    return
  }

  // Update subscription status
  const { error: updateSubError } = await supabaseAdmin
    .from('user_subscriptions')
    .update({ status: 'cancelled' })
    .eq('stripe_subscription_id', subscription.id)

  if (updateSubError) {
    console.error('Error updating subscription status:', updateSubError)
  }

  // Downgrade user to free plan
  const { error: updateUserError } = await supabaseAdmin
    .from('users')
    .update({
      subscription_tier: 'free',
      credits: 50, // Free plan credits
    })
    .eq('id', userSub.user_id)

  if (updateUserError) {
    console.error('Error downgrading user:', updateUserError)
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  console.log('Payment succeeded:', invoice.id)
  
  if (invoice.subscription) {
    // Reset user's monthly credits
    const { data: userSub, error: subError } = await supabaseAdmin
      .from('user_subscriptions')
      .select('user_id, plan_id')
      .eq('stripe_subscription_id', invoice.subscription)
      .single()

    if (subError || !userSub) {
      console.error('Subscription not found for invoice:', invoice.id)
      return
    }

    // Get plan details
    const { data: plan, error: planError } = await supabaseAdmin
      .from('subscription_plans')
      .select('message_limit')
      .eq('id', userSub.plan_id)
      .single()

    if (planError || !plan) {
      console.error('Plan not found:', userSub.plan_id)
      return
    }

    // Reset user credits
    const { error: updateError } = await supabaseAdmin
      .from('users')
      .update({ credits: plan.message_limit })
      .eq('id', userSub.user_id)

    if (updateError) {
      console.error('Error resetting user credits:', updateError)
    }
  }
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  console.log('Payment failed:', invoice.id)
  
  // You might want to send an email notification or take other actions
  // For now, just log the failure
  if (invoice.subscription) {
    const { data: userSub } = await supabaseAdmin
      .from('user_subscriptions')
      .select('user_id')
      .eq('stripe_subscription_id', invoice.subscription)
      .single()

    if (userSub) {
      console.log(`Payment failed for user: ${userSub.user_id}`)
      // TODO: Send email notification, show in-app notification, etc.
    }
  }
}

async function handleCustomerCreated(customer: Stripe.Customer) {
  console.log('Customer created:', customer.id)
  
  // Update user record with Stripe customer ID if email matches
  if (customer.email) {
    const { error } = await supabaseAdmin
      .from('users')
      .update({ stripe_customer_id: customer.id })
      .eq('email', customer.email)

    if (error) {
      console.error('Error updating user with customer ID:', error)
    }
  }
}
