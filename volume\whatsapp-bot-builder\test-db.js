const { PrismaClient } = require('@prisma/client')

async function testConnection() {
  const prisma = new PrismaClient()
  
  try {
    console.log('🔍 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connected successfully!')
    
    // Test if we can query (this will fail if tables don't exist, which is expected)
    try {
      const userCount = await prisma.user.count()
      console.log(`📊 Found ${userCount} users in database`)
    } catch (error) {
      if (error.code === 'P2021') {
        console.log('⚠️  Tables don\'t exist yet - need to run: npm run db:push')
      } else {
        console.log('⚠️  Query error (expected if schema not pushed):', error.message)
      }
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:')
    console.error('Error code:', error.code)
    console.error('Error message:', error.message)
    
    if (error.message.includes('Tenant or user not found')) {
      console.log('\n🔧 Possible fixes:')
      console.log('1. Check if your database password is correct')
      console.log('2. Verify your Supabase project is active')
      console.log('3. Check if your IP is whitelisted in Supabase')
    }
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
