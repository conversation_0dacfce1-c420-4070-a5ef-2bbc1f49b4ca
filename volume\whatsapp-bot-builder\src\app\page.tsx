'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Bot, MessageCircle, Zap, Users, ArrowRight, Check } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect to dashboard
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Bot className="h-8 w-8 text-blue-600 mr-2" />
            <span className="text-xl font-bold text-gray-900">WhatsApp Bot Builder</span>
          </div>
          <div className="space-x-4">
            <Link href="/auth">
              <Button variant="ghost">Sign In</Button>
            </Link>
            <Link href="/auth">
              <Button>Get Started</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-5xl font-bold text-gray-900 mb-6">
          Build AI-Powered WhatsApp Bots
          <span className="text-blue-600"> in Minutes</span>
        </h1>
        <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
          Create intelligent customer support bots for WhatsApp without any coding.
          Train your AI, customize responses, and go live instantly.
        </p>
        <div className="space-x-4">
          <Link href="/auth">
            <Button size="lg" className="text-lg px-8 py-3">
              Start Building Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
          <Button variant="outline" size="lg" className="text-lg px-8 py-3">
            Watch Demo
          </Button>
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Everything you need to automate customer support
          </h2>
          <p className="text-lg text-gray-600">
            Powerful features designed for businesses of all sizes
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
            <MessageCircle className="h-12 w-12 text-blue-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-3">Smart AI Responses</h3>
            <p className="text-gray-600">
              GPT-powered conversations that understand context and provide helpful,
              human-like responses to your customers.
            </p>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
            <Zap className="h-12 w-12 text-blue-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-3">No-Code Builder</h3>
            <p className="text-gray-600">
              Simple drag-and-drop interface to create and customize your bot.
              No technical knowledge required.
            </p>
          </div>

          <div className="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
            <Users className="h-12 w-12 text-blue-600 mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-3">24/7 Support</h3>
            <p className="text-gray-600">
              Your bot works around the clock, handling customer inquiries
              and providing instant responses.
            </p>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Simple, transparent pricing
          </h2>
          <p className="text-lg text-gray-600">
            Start free, upgrade as you grow
          </p>
        </div>

        <div className="grid md:grid-cols-4 gap-6 max-w-6xl mx-auto">
          {/* Free Plan */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Free</h3>
            <div className="text-3xl font-bold text-gray-900 mb-4">₹0</div>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                50 AI messages
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                1 bot
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Basic analytics
              </li>
            </ul>
            <Link href="/auth">
              <Button variant="outline" className="w-full">Get Started</Button>
            </Link>
          </div>

          {/* Starter Plan */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Starter</h3>
            <div className="text-3xl font-bold text-gray-900 mb-4">₹499</div>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                1,000 AI messages
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                3 bots
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Advanced analytics
              </li>
            </ul>
            <Link href="/auth">
              <Button className="w-full">Choose Plan</Button>
            </Link>
          </div>

          {/* Pro Plan */}
          <div className="bg-blue-50 rounded-lg p-6 shadow-sm border-2 border-blue-200 relative">
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                Most Popular
              </span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Pro</h3>
            <div className="text-3xl font-bold text-gray-900 mb-4">₹1,499</div>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                5,000 AI messages
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                10 bots
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Priority support
              </li>
            </ul>
            <Link href="/auth">
              <Button className="w-full">Choose Plan</Button>
            </Link>
          </div>

          {/* Business Plan */}
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Business</h3>
            <div className="text-3xl font-bold text-gray-900 mb-4">₹4,999</div>
            <ul className="space-y-3 mb-6">
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                25,000 AI messages
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Unlimited bots
              </li>
              <li className="flex items-center text-sm text-gray-600">
                <Check className="h-4 w-4 text-green-500 mr-2" />
                Team access
              </li>
            </ul>
            <Link href="/auth">
              <Button variant="outline" className="w-full">Contact Sales</Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to automate your customer support?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of businesses using AI to improve customer experience
          </p>
          <Link href="/auth">
            <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
              Start Building Your Bot
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center">
            <Bot className="h-6 w-6 text-blue-400 mr-2" />
            <span className="text-lg font-semibold">WhatsApp Bot Builder</span>
          </div>
          <p className="text-center text-gray-400 mt-4">
            © 2024 WhatsApp Bot Builder. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
