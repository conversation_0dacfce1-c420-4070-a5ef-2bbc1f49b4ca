import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
})

export interface BotConfig {
  name: string
  businessContext: string
  tone: string
  greetingMessage: string
  knowledgeBase?: string[]
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export async function generateBotResponse(
  userMessage: string,
  botConfig: BotConfig,
  conversationHistory: ChatMessage[] = []
): Promise<string> {
  try {
    const systemPrompt = createSystemPrompt(botConfig)
    
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt },
      ...conversationHistory.slice(-10), // Keep last 10 messages for context
      { role: 'user', content: userMessage }
    ]

    const response = await openai.chat.completions.create({
      model: 'deepseek/deepseek-r1',
      messages,
      max_tokens: 500,
      temperature: 0.7,
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    })

    return response.choices[0]?.message?.content || 'I apologize, but I cannot process your request right now. Please try again later.'
  } catch (error) {
    console.error('OpenAI API error:', error)
    return 'I apologize, but I cannot process your request right now. Please try again later.'
  }
}

function createSystemPrompt(botConfig: BotConfig): string {
  const { name, businessContext, tone, greetingMessage, knowledgeBase } = botConfig
  
  let prompt = `You are ${name}, an AI assistant for WhatsApp. Here's your configuration:

BUSINESS CONTEXT:
${businessContext}

COMMUNICATION STYLE:
- Tone: ${tone}
- Always be helpful, professional, and friendly
- Keep responses concise and relevant for WhatsApp (max 2-3 sentences usually)
- Use emojis appropriately but don't overuse them
- If you don't know something, be honest and offer to help in other ways

GREETING MESSAGE:
When someone first contacts you, use this greeting: "${greetingMessage}"

IMPORTANT RULES:
1. Stay in character as a business assistant
2. Don't reveal that you're an AI unless directly asked
3. Focus on helping customers with business-related queries
4. If asked about topics outside your business scope, politely redirect
5. Always maintain the specified tone: ${tone}
6. Keep responses WhatsApp-friendly (short, clear, actionable)`

  if (knowledgeBase && knowledgeBase.length > 0) {
    prompt += `\n\nKNOWLEDGE BASE:
Use this information to answer customer questions:
${knowledgeBase.join('\n\n')}`
  }

  prompt += `\n\nRemember: You're representing this business on WhatsApp. Be helpful, stay on-topic, and maintain the ${tone} tone throughout the conversation.`

  return prompt
}

export async function generateGreeting(botConfig: BotConfig): Promise<string> {
  if (botConfig.greetingMessage) {
    return botConfig.greetingMessage
  }

  try {
    const response = await openai.chat.completions.create({
      model: 'deepseek/deepseek-r1',
      messages: [
        {
          role: 'system',
          content: `Generate a friendly WhatsApp greeting message for a business bot.
          Business context: ${botConfig.businessContext}
          Tone: ${botConfig.tone}
          Keep it short, welcoming, and professional. Include an emoji.`
        }
      ],
      max_tokens: 100,
      temperature: 0.8,
    })

    return response.choices[0]?.message?.content || `Hello! 👋 Welcome to ${botConfig.name}. How can I help you today?`
  } catch (error) {
    console.error('Error generating greeting:', error)
    return `Hello! 👋 Welcome to ${botConfig.name}. How can I help you today?`
  }
}

export async function extractBusinessInfo(description: string): Promise<{
  suggestedName: string
  suggestedContext: string
  suggestedTone: string
}> {
  try {
    const response = await openai.chat.completions.create({
      model: 'deepseek/deepseek-r1',
      messages: [
        {
          role: 'system',
          content: `Analyze this business description and extract:
          1. A suggested business name (if not provided)
          2. A clear business context summary
          3. A suggested communication tone (professional, friendly, casual, formal)

          Return as JSON with keys: suggestedName, suggestedContext, suggestedTone`
        },
        {
          role: 'user',
          content: description
        }
      ],
      max_tokens: 300,
      temperature: 0.3,
    })

    const result = JSON.parse(response.choices[0]?.message?.content || '{}')
    return {
      suggestedName: result.suggestedName || 'My Business',
      suggestedContext: result.suggestedContext || description,
      suggestedTone: result.suggestedTone || 'friendly'
    }
  } catch (error) {
    console.error('Error extracting business info:', error)
    return {
      suggestedName: 'My Business',
      suggestedContext: description,
      suggestedTone: 'friendly'
    }
  }
}
