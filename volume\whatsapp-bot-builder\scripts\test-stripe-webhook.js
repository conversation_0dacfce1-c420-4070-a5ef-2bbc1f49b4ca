#!/usr/bin/env node

/**
 * Test script for Stripe webhooks
 * This script helps you test webhook events locally
 */

const crypto = require('crypto')
const fetch = require('node-fetch')

// Configuration
const WEBHOOK_URL = 'http://localhost:3000/api/webhooks/stripe'
const WEBHOOK_SECRET = 'whsec_test_secret' // Use your actual webhook secret for testing

// Sample webhook events for testing
const sampleEvents = {
  'customer.subscription.created': {
    id: 'evt_test_webhook',
    object: 'event',
    api_version: '2024-12-18.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'sub_test_subscription',
        object: 'subscription',
        customer: 'cus_test_customer',
        status: 'active',
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000),
        items: {
          data: [
            {
              price: {
                id: 'price_test_starter'
              }
            }
          ]
        }
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request',
      idempotency_key: null
    },
    type: 'customer.subscription.created'
  },

  'invoice.payment_succeeded': {
    id: 'evt_test_webhook_payment',
    object: 'event',
    api_version: '2024-12-18.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'in_test_invoice',
        object: 'invoice',
        customer: 'cus_test_customer',
        subscription: 'sub_test_subscription',
        status: 'paid',
        amount_paid: 49900, // $499.00 in cents
        currency: 'inr'
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request',
      idempotency_key: null
    },
    type: 'invoice.payment_succeeded'
  },

  'customer.subscription.deleted': {
    id: 'evt_test_webhook_cancel',
    object: 'event',
    api_version: '2024-12-18.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'sub_test_subscription',
        object: 'subscription',
        customer: 'cus_test_customer',
        status: 'canceled',
        canceled_at: Math.floor(Date.now() / 1000),
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor((Date.now() + 30 * 24 * 60 * 60 * 1000) / 1000)
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request',
      idempotency_key: null
    },
    type: 'customer.subscription.deleted'
  }
}

function generateSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000)
  const signedPayload = `${timestamp}.${payload}`
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex')
  
  return `t=${timestamp},v1=${signature}`
}

async function sendWebhook(eventType) {
  const event = sampleEvents[eventType]
  
  if (!event) {
    console.error(`Unknown event type: ${eventType}`)
    console.log('Available events:', Object.keys(sampleEvents))
    return
  }

  const payload = JSON.stringify(event)
  const signature = generateSignature(payload, WEBHOOK_SECRET)

  try {
    console.log(`\n🚀 Sending ${eventType} webhook...`)
    console.log('Payload:', JSON.stringify(event, null, 2))
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': signature,
      },
      body: payload,
    })

    const responseText = await response.text()
    
    if (response.ok) {
      console.log('✅ Webhook sent successfully!')
      console.log('Response:', responseText)
    } else {
      console.error('❌ Webhook failed!')
      console.error('Status:', response.status)
      console.error('Response:', responseText)
    }
  } catch (error) {
    console.error('❌ Error sending webhook:', error.message)
  }
}

// CLI interface
const eventType = process.argv[2]

if (!eventType) {
  console.log('Usage: node test-stripe-webhook.js <event_type>')
  console.log('\nAvailable event types:')
  Object.keys(sampleEvents).forEach(type => {
    console.log(`  - ${type}`)
  })
  process.exit(1)
}

if (eventType === 'all') {
  console.log('🧪 Testing all webhook events...')
  
  const runAllTests = async () => {
    for (const type of Object.keys(sampleEvents)) {
      await sendWebhook(type)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second between tests
    }
  }
  
  runAllTests()
} else {
  sendWebhook(eventType)
}
