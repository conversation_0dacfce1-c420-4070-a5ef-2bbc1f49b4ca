import { NextRequest, NextResponse } from 'next/server'
import { updateUserCredits } from '@/lib/database'

export async function PATCH(req: NextRequest) {
  try {
    const { userId, credits } = await req.json()
    
    if (!userId || typeof credits !== 'number') {
      return NextResponse.json(
        { error: 'User ID and credits are required' },
        { status: 400 }
      )
    }
    
    const success = await updateUserCredits(userId, credits)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update user credits' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in PATCH /api/users/credits:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
