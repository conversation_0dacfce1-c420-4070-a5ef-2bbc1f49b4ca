import { NextRequest, NextResponse } from 'next/server'
import { getBotById, updateBot } from '@/lib/database'

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const bot = await getBotById(params.id)
    
    if (!bot) {
      return NextResponse.json(
        { error: 'Bot not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json(bot)
  } catch (error) {
    console.error('Error in GET /api/bots/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const updates = await req.json()
    
    const success = await updateBot(params.id, updates)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update bot' },
        { status: 500 }
      )
    }
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in PATCH /api/bots/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
