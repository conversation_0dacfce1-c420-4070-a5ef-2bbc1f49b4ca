# Database Configuration (Prisma + Supabase PostgreSQL)
DATABASE_URL="postgresql://postgres.pemxsygvxytkltsajmlg:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1"

# Supabase Configuration (for Auth)
NEXT_PUBLIC_SUPABASE_URL=https://pemxsygvxytkltsajmlg.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ulssZcnGCwh8E6hBxHRfr48sTo1EyZSMtYY1I2ZFSXo
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.tn0b7yGlmdr1pkVHtVgmnDdwfJrM3-S7OMPf5dL0Sh4

# OpenRouter Configuration (using DeepSeek R1)
OPENROUTER_API_KEY=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

# WhatsApp/Twilio Configuration
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=73a8350b4a672aa5ab3500aa731878b0
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_tR3PYbcVNZZ796tH88S4VQ2u
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51BTUDGJAJfZb9HEBwDg86TN1KNprHjkfipXmEDMb0gSCassK5T3ZfxsAbcgKVmAIXF7oZ6ItlZZbXO6idTHE67IM007EwQ4uN3
STRIPE_WEBHOOK_SECRET=whsec_test_secret

# App Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here