import { PrismaClient, SubscriptionTier } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create subscription plans
  const plans = [
    {
      name: 'Free Plan',
      tier: SubscriptionTier.FREE,
      priceMonthly: 0,
      priceYearly: 0,
      messageLimit: 50,
      features: {
        analytics: false,
        prioritySupport: false,
        customBranding: false,
      },
      isActive: true,
    },
    {
      name: 'Starter Plan',
      tier: SubscriptionTier.STARTER,
      priceMonthly: 499,
      priceYearly: 4990,
      messageLimit: 1000,
      features: {
        analytics: true,
        prioritySupport: false,
        customBranding: false,
      },
      stripePriceId: 'price_starter_monthly',
      isActive: true,
    },
    {
      name: 'Pro Plan',
      tier: SubscriptionTier.PRO,
      priceMonthly: 1499,
      priceYearly: 14990,
      messageLimit: 5000,
      features: {
        analytics: true,
        prioritySupport: true,
        customBranding: true,
      },
      stripePriceId: 'price_pro_monthly',
      isActive: true,
    },
    {
      name: 'Business Plan',
      tier: SubscriptionTier.BUSINESS,
      priceMonthly: 4999,
      priceYearly: 49990,
      messageLimit: 25000,
      features: {
        analytics: true,
        prioritySupport: true,
        customBranding: true,
        teamAccess: true,
      },
      stripePriceId: 'price_business_monthly',
      isActive: true,
    },
  ]

  for (const plan of plans) {
    await prisma.subscriptionPlan.upsert({
      where: { tier: plan.tier },
      update: plan,
      create: plan,
    })
    console.log(`✅ Created/updated ${plan.name}`)
  }

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
