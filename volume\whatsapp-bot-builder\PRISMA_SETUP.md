# Prisma Setup Guide

This project uses <PERSON><PERSON><PERSON> as the ORM for database operations, while keeping <PERSON><PERSON><PERSON> for authentication.

## Architecture

- **Authentication**: Supabase Auth
- **Database**: Supabase PostgreSQL (accessed via Prisma)
- **ORM**: Prisma

## Environment Setup

Make sure your `.env.local` file has the correct DATABASE_URL:

```bash
# Replace [YOUR-PASSWORD] with your actual Supabase database password
DATABASE_URL="postgresql://postgres.pemxsygvxytkltsajmlg:[YOUR-PASSWORD]@aws-0-ap-south-1.pooler.supabase.com:6543/postgres?pgbouncer=true&connection_limit=1"
```

## Getting Your Database Password

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to **Settings** → **Database**
4. Copy the password you set when creating the project
5. Replace `[YOUR-PASSWORD]` in the DATABASE_URL

## Available Commands

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (creates tables)
npm run db:push

# Seed database with initial data
npm run db:seed

# Open Prisma Studio (database GUI)
npm run db:studio
```

## Initial Setup

1. **Update DATABASE_URL** with your actual password
2. **Push schema to database**:
   ```bash
   npm run db:push
   ```
3. **Seed the database**:
   ```bash
   npm run db:seed
   ```

## Database Schema

The schema includes:

- **Users**: User accounts and profiles
- **Bots**: WhatsApp bot configurations
- **Conversations**: Customer conversations
- **Messages**: Individual messages in conversations
- **KnowledgeBase**: Bot training data
- **SubscriptionPlans**: Available subscription tiers
- **UserSubscriptions**: User subscription records
- **MessageUsage**: Daily message usage tracking

## Key Features

- **Type Safety**: Full TypeScript support
- **Relations**: Proper foreign key relationships
- **Enums**: Type-safe enums for status fields
- **Soft Deletes**: Cascade deletes where appropriate
- **Indexing**: Optimized queries with proper indexes

## Development Workflow

1. **Make schema changes** in `prisma/schema.prisma`
2. **Generate client**: `npm run db:generate`
3. **Push to database**: `npm run db:push`
4. **Update seed data** if needed in `prisma/seed.ts`

## Production Deployment

For production, you'll need to:

1. Set up a production database
2. Update DATABASE_URL in production environment
3. Run migrations: `npx prisma migrate deploy`
4. Seed production data: `npm run db:seed`

## Prisma Studio

To explore your database visually:

```bash
npm run db:studio
```

This opens a web interface at `http://localhost:5555` where you can:
- View all tables and data
- Edit records
- Run queries
- Manage relationships

## Common Operations

### Creating a new user:
```typescript
import { prisma } from '@/lib/prisma'

const user = await prisma.user.create({
  data: {
    email: '<EMAIL>',
    fullName: 'John Doe',
  },
})
```

### Finding user with bots:
```typescript
const userWithBots = await prisma.user.findUnique({
  where: { id: userId },
  include: { bots: true },
})
```

### Creating a bot:
```typescript
const bot = await prisma.bot.create({
  data: {
    userId: user.id,
    name: 'My Bot',
    businessContext: 'Customer support bot',
    greetingMessage: 'Hello! How can I help you?',
  },
})
```

## Troubleshooting

### Connection Issues
- Verify DATABASE_URL is correct
- Check if your IP is whitelisted in Supabase
- Ensure database password is correct

### Schema Issues
- Run `npm run db:generate` after schema changes
- Use `npm run db:push` to sync schema with database
- Check Prisma logs for detailed error messages

### Migration Issues
- For development, use `db:push` instead of migrations
- For production, use proper migrations with `prisma migrate`
