// Client-side API functions for database operations
// These functions make HTTP requests to our API routes instead of using Prisma directly

export interface CreateUserData {
  id: string
  email: string
  fullName?: string
  avatarUrl?: string
}

export interface User {
  id: string
  email: string
  fullName: string | null
  avatarUrl: string | null
  credits: number
  subscriptionTier: string
  stripeCustomerId: string | null
  createdAt: string
  updatedAt: string
}

export interface Bot {
  id: string
  userId: string
  name: string
  description: string | null
  businessContext: string
  tone: string
  greetingMessage: string
  whatsappNumber: string | null
  logoUrl: string | null
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// User operations
export async function createUser(userData: CreateUserData): Promise<User | null> {
  try {
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    })

    if (!response.ok) {
      console.error('Failed to create user:', await response.text())
      return null
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating user:', error)
    return null
  }
}

export async function getUserById(userId: string): Promise<User | null> {
  try {
    const response = await fetch(`/api/users?id=${userId}`)

    if (!response.ok) {
      if (response.status === 404) {
        return null
      }
      console.error('Failed to fetch user:', await response.text())
      return null
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching user:', error)
    return null
  }
}

export async function updateUserCredits(userId: string, credits: number): Promise<boolean> {
  try {
    const response = await fetch('/api/users/credits', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userId, credits }),
    })

    return response.ok
  } catch (error) {
    console.error('Error updating user credits:', error)
    return false
  }
}

// Bot operations
export async function createBot(botData: {
  userId: string
  name: string
  description?: string
  businessContext: string
  tone?: string
  greetingMessage: string
  whatsappNumber?: string
  logoUrl?: string
}): Promise<Bot | null> {
  try {
    const response = await fetch('/api/bots', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(botData),
    })

    if (!response.ok) {
      console.error('Failed to create bot:', await response.text())
      return null
    }

    return await response.json()
  } catch (error) {
    console.error('Error creating bot:', error)
    return null
  }
}

export async function getUserBots(userId: string): Promise<Bot[]> {
  try {
    const response = await fetch(`/api/bots?userId=${userId}`)

    if (!response.ok) {
      console.error('Failed to fetch user bots:', await response.text())
      return []
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching user bots:', error)
    return []
  }
}

export async function getBotById(botId: string): Promise<Bot | null> {
  try {
    const response = await fetch(`/api/bots/${botId}`)

    if (!response.ok) {
      if (response.status === 404) {
        return null
      }
      console.error('Failed to fetch bot:', await response.text())
      return null
    }

    return await response.json()
  } catch (error) {
    console.error('Error fetching bot:', error)
    return null
  }
}

export async function updateBot(botId: string, updates: Partial<Bot>): Promise<boolean> {
  try {
    const response = await fetch(`/api/bots/${botId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    })

    return response.ok
  } catch (error) {
    console.error('Error updating bot:', error)
    return false
  }
}
